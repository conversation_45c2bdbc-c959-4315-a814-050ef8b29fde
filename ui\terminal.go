package ui

import (
	"context"
	"fmt"
	"strings"
	"time"

	"arien-ai/config"
	"arien-ai/prompts"
	"arien-ai/providers"
	"arien-ai/session"
	"arien-ai/tools"
	"arien-ai/utils"

	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// TerminalModel represents the main terminal UI model
type TerminalModel struct {
	// Core components
	provider      providers.Provider
	toolManager   *tools.ToolManager
	session       *session.Session
	systemPrompt  *prompts.SystemPrompt

	// UI components
	header        *HeaderComponent
	thinking      *ThinkingComponent
	slashCommands *SlashCommandsComponent

	// UI state
	input         textinput.Model
	messages      []Message
	isThinking    bool
	thinkingMsg   string

	// Dimensions
	width  int
	height int

	// Styles
	styles Styles

	// Context
	ctx    context.Context
	cancel context.CancelFunc
}

// Message represents a chat message
type Message struct {
	Role      string
	Content   string
	Timestamp time.Time
	ToolCalls []providers.ToolCall
	Error     string
}

// Styles holds the styling configuration
type Styles struct {
	Header      lipgloss.Style
	UserMsg     lipgloss.Style
	AssistantMsg lipgloss.Style
	SystemMsg   lipgloss.Style
	ErrorMsg    lipgloss.Style
	ThinkingMsg lipgloss.Style
	Input       lipgloss.Style
	Border      lipgloss.Style
}

// NewTerminalModel creates a new terminal model
func NewTerminalModel(provider providers.Provider, toolManager *tools.ToolManager, sess *session.Session) *TerminalModel {
	ctx, cancel := context.WithCancel(context.Background())

	input := textinput.New()
	input.Placeholder = "Type your message or command..."
	input.Focus()
	input.CharLimit = 1000
	input.Width = 80

	styles := NewStyles()

	// Initialize system prompt
	systemPrompt, err := prompts.NewSystemPrompt()
	if err != nil {
		utils.LogError("Failed to initialize system prompt: %v", err)
	}

	return &TerminalModel{
		provider:      provider,
		toolManager:   toolManager,
		session:       sess,
		systemPrompt:  systemPrompt,
		header:        NewHeaderComponent(styles),
		thinking:      NewThinkingComponent(styles),
		slashCommands: NewSlashCommandsComponent(styles),
		input:         input,
		messages:      []Message{},
		ctx:           ctx,
		cancel:        cancel,
		styles:        styles,
	}
}

// NewStyles creates the default styling configuration
func NewStyles() Styles {
	return Styles{
		Header: lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("39")).
			BorderStyle(lipgloss.NormalBorder()).
			BorderBottom(true).
			Padding(0, 1),
		
		UserMsg: lipgloss.NewStyle().
			Foreground(lipgloss.Color("46")).
			Bold(true).
			Margin(1, 0),
		
		AssistantMsg: lipgloss.NewStyle().
			Foreground(lipgloss.Color("33")).
			Margin(1, 0),
		
		SystemMsg: lipgloss.NewStyle().
			Foreground(lipgloss.Color("240")).
			Italic(true).
			Margin(1, 0),
		
		ErrorMsg: lipgloss.NewStyle().
			Foreground(lipgloss.Color("196")).
			Bold(true).
			Margin(1, 0),
		
		ThinkingMsg: lipgloss.NewStyle().
			Foreground(lipgloss.Color("214")).
			Italic(true),
		
		Input: lipgloss.NewStyle().
			BorderStyle(lipgloss.NormalBorder()).
			BorderTop(true).
			Padding(1, 1),
		
		Border: lipgloss.NewStyle().
			BorderStyle(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("240")),
	}
}

// Init initializes the terminal model
func (m *TerminalModel) Init() tea.Cmd {
	return tea.Batch(
		textinput.Blink,
		m.tickThinking(),
	)
}

// Update handles terminal events
func (m *TerminalModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd
	
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			m.cancel()
			return m, tea.Quit
		case "enter":
			if m.isThinking {
				return m, nil
			}
			return m.handleInput()
		case "ctrl+l":
			m.messages = []Message{}
			m.session.ClearMessages()
			return m, nil
		}
	
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.input.Width = msg.Width - 4
		return m, nil
	
	case thinkingTickMsg:
		if m.isThinking {
			m.thinking.NextFrame()
			return m, m.tickThinking()
		}
		return m, nil

	case responseMsg:
		m.isThinking = false
		m.addMessage(Message{
			Role:      "assistant",
			Content:   msg.content,
			Timestamp: time.Now(),
			ToolCalls: msg.toolCalls,
		})

		// Execute tool calls if present
		if len(msg.toolCalls) > 0 {
			return m, m.executeToolCalls(msg.toolCalls)
		}
		return m, nil

	case errorMsg:
		m.isThinking = false
		m.addMessage(Message{
			Role:      "system",
			Content:   "",
			Timestamp: time.Now(),
			Error:     msg.error,
		})
		return m, nil

	case toolResultMsg:
		// Add tool result to conversation
		m.addMessage(Message{
			Role:      "system",
			Content:   fmt.Sprintf("Tool execution result: %s", msg.result),
			Timestamp: time.Now(),
		})
		return m, nil
	}
	
	var cmd tea.Cmd
	m.input, cmd = m.input.Update(msg)
	cmds = append(cmds, cmd)
	
	return m, tea.Batch(cmds...)
}

// View renders the terminal interface
func (m *TerminalModel) View() string {
	var sections []string

	// Header
	headerView := m.header.Render(m.session, m.width)
	sections = append(sections, headerView)

	// Messages
	messageHeight := m.height - 8 // Reserve space for header and input
	messageView := m.renderMessages(messageHeight)
	sections = append(sections, messageView)

	// Thinking indicator
	if m.isThinking {
		thinkingView := m.thinking.Render(m.thinkingMsg)
		sections = append(sections, thinkingView)
	}

	// Input
	inputView := m.styles.Input.Render(m.input.View())
	sections = append(sections, inputView)

	return lipgloss.JoinVertical(lipgloss.Left, sections...)
}

// renderMessages renders the message history
func (m *TerminalModel) renderMessages(maxHeight int) string {
	if len(m.messages) == 0 {
		return m.styles.SystemMsg.Render("Welcome to Arien AI! Type your message or use slash commands (/help for more info)")
	}
	
	var rendered []string
	for _, msg := range m.messages {
		rendered = append(rendered, m.renderMessage(msg))
	}
	
	content := strings.Join(rendered, "\n")
	
	// Truncate if too long
	lines := strings.Split(content, "\n")
	if len(lines) > maxHeight {
		lines = lines[len(lines)-maxHeight:]
		content = strings.Join(lines, "\n")
	}
	
	return content
}

// renderMessage renders a single message
func (m *TerminalModel) renderMessage(msg Message) string {
	timestamp := ""
	if config.Get().Terminal.ShowTimestamps {
		timestamp = fmt.Sprintf("[%s] ", msg.Timestamp.Format("15:04:05"))
	}
	
	var content string
	var style lipgloss.Style
	
	switch msg.Role {
	case "user":
		style = m.styles.UserMsg
		content = fmt.Sprintf("%sYou: %s", timestamp, msg.Content)
	case "assistant":
		style = m.styles.AssistantMsg
		content = fmt.Sprintf("%sAI: %s", timestamp, msg.Content)
		
		// Add tool calls if present
		if len(msg.ToolCalls) > 0 {
			for _, toolCall := range msg.ToolCalls {
				content += fmt.Sprintf("\n🔧 Executing: %s", toolCall.Function.Name)
			}
		}
	case "system":
		style = m.styles.SystemMsg
		if msg.Error != "" {
			style = m.styles.ErrorMsg
			content = fmt.Sprintf("%s❌ Error: %s", timestamp, msg.Error)
		} else {
			content = fmt.Sprintf("%s💡 %s", timestamp, msg.Content)
		}
	}
	
	return style.Render(content)
}

// addMessage adds a new message to the history
func (m *TerminalModel) addMessage(msg Message) {
	m.messages = append(m.messages, msg)
	
	// Save to session
	sessionMsg := session.Message{
		Role:      msg.Role,
		Content:   msg.Content,
		Timestamp: msg.Timestamp,
	}
	m.session.AddMessage(sessionMsg)
	
	// Limit message history
	maxHistory := config.Get().Session.MaxHistory
	if len(m.messages) > maxHistory {
		m.messages = m.messages[len(m.messages)-maxHistory:]
	}
}

// handleInput processes user input
func (m *TerminalModel) handleInput() (tea.Model, tea.Cmd) {
	input := strings.TrimSpace(m.input.Value())
	if input == "" {
		return m, nil
	}

	// Clear input
	m.input.SetValue("")

	// Add user message
	m.addMessage(Message{
		Role:      "user",
		Content:   input,
		Timestamp: time.Now(),
	})

	// Handle slash commands
	if strings.HasPrefix(input, "/") {
		return m.handleSlashCommand(input)
	}

	// Start thinking animation
	m.isThinking = true
	m.thinkingMsg = "Thinking..."
	m.thinking.Reset()

	// Send to AI
	return m, m.sendToAI(input)
}

// handleSlashCommand processes slash commands
func (m *TerminalModel) handleSlashCommand(command string) (tea.Model, tea.Cmd) {
	result, err := m.slashCommands.ProcessCommand(command, m.session)

	if err != nil {
		m.addMessage(Message{
			Role:      "system",
			Content:   "",
			Timestamp: time.Now(),
			Error:     err.Error(),
		})
	} else {
		m.addMessage(Message{
			Role:      "system",
			Content:   result,
			Timestamp: time.Now(),
		})

		// Handle special commands
		if strings.HasPrefix(command, "/quit") {
			m.cancel()
			return m, tea.Quit
		}
	}

	return m, nil
}

// Message types for tea.Cmd
type thinkingTickMsg struct{}
type responseMsg struct {
	content   string
	toolCalls []providers.ToolCall
}
type errorMsg struct {
	error string
}
type toolResultMsg struct {
	result string
}

// tickThinking creates a thinking animation tick
func (m *TerminalModel) tickThinking() tea.Cmd {
	return tea.Tick(time.Millisecond*200, func(t time.Time) tea.Msg {
		return thinkingTickMsg{}
	})
}

// sendToAI sends the message to the AI provider
func (m *TerminalModel) sendToAI(input string) tea.Cmd {
	return func() tea.Msg {
		// Prepare messages for AI including system prompt
		messages := []providers.Message{}

		// Add system prompt if available
		if m.systemPrompt != nil {
			messages = append(messages, m.systemPrompt.GetSystemMessage())
		}

		// Add conversation history
		for _, msg := range m.messages {
			if msg.Role != "system" || msg.Error == "" {
				messages = append(messages, providers.Message{
					Role:    msg.Role,
					Content: msg.Content,
				})
			}
		}

		// Create chat request with retry logic
		req := &providers.ChatRequest{
			Messages:    messages,
			Tools:       m.toolManager.GetToolDefinitions(),
			ToolChoice:  "auto",
			Temperature: 0.7,
			MaxTokens:   4000,
		}

		// Send request with retry logic
		var resp *providers.ChatResponse
		var err error

		retryErr := utils.Retry(m.ctx, func() error {
			resp, err = m.provider.Chat(m.ctx, req)
			return err
		})

		if retryErr != nil {
			utils.LogError("Failed to get AI response: %v", retryErr)
			return errorMsg{error: retryErr.Error()}
		}

		if len(resp.Choices) == 0 {
			return errorMsg{error: "No response from AI"}
		}

		choice := resp.Choices[0]
		return responseMsg{
			content:   choice.Message.Content,
			toolCalls: choice.Message.ToolCalls,
		}
	}
}

// executeToolCalls executes tool calls from the AI
func (m *TerminalModel) executeToolCalls(toolCalls []providers.ToolCall) tea.Cmd {
	return func() tea.Msg {
		var results []string

		for _, toolCall := range toolCalls {
			utils.LogInfo("Executing tool: %s", toolCall.Function.Name)

			// Check if user confirmation is required
			cfg := config.Get()
			if cfg.Terminal.ConfirmCommands && !cfg.Terminal.AutoApprove {
				// TODO: Implement user confirmation dialog
				utils.LogInfo("Tool execution requires user confirmation (not implemented)")
			}

			// Execute the tool
			result, err := m.toolManager.ExecuteTool(m.ctx, toolCall.Function.Name, toolCall.Function.Arguments)
			if err != nil {
				results = append(results, fmt.Sprintf("Error executing %s: %v", toolCall.Function.Name, err))
				continue
			}

			if result.Success {
				results = append(results, fmt.Sprintf("✅ %s: %s", toolCall.Function.Name, result.Output))
			} else {
				results = append(results, fmt.Sprintf("❌ %s failed: %s", toolCall.Function.Name, result.Error))
			}
		}

		return toolResultMsg{result: strings.Join(results, "\n")}
	}
}
