package ui

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"arien-ai/config"
	"arien-ai/session"
)

// HeaderComponent renders the terminal header
type HeaderComponent struct {
	styles Styles
}

// NewHeaderComponent creates a new header component
func NewHeaderComponent(styles Styles) *HeaderComponent {
	return &HeaderComponent{
		styles: styles,
	}
}

// <PERSON><PERSON> renders the header with current session info
func (h *HeaderComponent) Render(sess *session.Session, width int) string {
	cfg := config.Get()
	
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		cwd = "unknown"
	} else {
		// Show only the last 2 directories for brevity
		parts := strings.Split(filepath.Clean(cwd), string(filepath.Separator))
		if len(parts) > 2 {
			cwd = ".../" + strings.Join(parts[len(parts)-2:], "/")
		}
	}

	// Create header sections
	leftSection := fmt.Sprintf("🤖 Arien AI Terminal")
	
	centerSection := fmt.Sprintf("Provider: %s | Model: %s", 
		cfg.Provider.Name, cfg.Provider.Model)
	
	rightSection := fmt.Sprintf("Session: %s | Dir: %s", 
		sess.ID[:8], cwd)

	// Calculate spacing
	totalContentWidth := len(leftSection) + len(centerSection) + len(rightSection)
	if totalContentWidth >= width-4 {
		// Truncate if too long
		centerSection = fmt.Sprintf("%s|%s", cfg.Provider.Name, cfg.Provider.Model)
		rightSection = fmt.Sprintf("%s|%s", sess.ID[:6], filepath.Base(cwd))
	}

	// Create header line
	padding := width - len(leftSection) - len(centerSection) - len(rightSection)
	leftPadding := padding / 3
	rightPadding := padding - leftPadding

	headerLine := leftSection + 
		strings.Repeat(" ", leftPadding) + 
		centerSection + 
		strings.Repeat(" ", rightPadding) + 
		rightSection

	return h.styles.Header.Width(width).Render(headerLine)
}

// ThinkingComponent handles the thinking animation
type ThinkingComponent struct {
	styles     Styles
	ballFrames []string
	frame      int
	startTime  time.Time
}

// NewThinkingComponent creates a new thinking component
func NewThinkingComponent(styles Styles) *ThinkingComponent {
	ballFrames := []string{
		"( ●    )",
		"(  ●   )",
		"(   ●  )",
		"(    ● )",
		"(     ●)",
		"(    ● )",
		"(   ●  )",
		"(  ●   )",
		"( ●    )",
		"(●     )",
	}

	return &ThinkingComponent{
		styles:     styles,
		ballFrames: ballFrames,
		frame:      0,
		startTime:  time.Now(),
	}
}

// NextFrame advances to the next animation frame
func (t *ThinkingComponent) NextFrame() {
	t.frame = (t.frame + 1) % len(t.ballFrames)
}

// Render renders the thinking animation with elapsed time
func (t *ThinkingComponent) Render(message string) string {
	elapsed := time.Since(t.startTime)
	elapsedSeconds := int(elapsed.Seconds())
	
	var timeDisplay string
	if elapsedSeconds < 60 {
		timeDisplay = fmt.Sprintf("%ds", elapsedSeconds)
	} else {
		minutes := elapsedSeconds / 60
		seconds := elapsedSeconds % 60
		timeDisplay = fmt.Sprintf("%dm%ds", minutes, seconds)
	}

	thinkingText := fmt.Sprintf("%s %s (%s)", 
		t.ballFrames[t.frame], message, timeDisplay)
	
	return t.styles.ThinkingMsg.Render(thinkingText)
}

// Reset resets the thinking animation
func (t *ThinkingComponent) Reset() {
	t.frame = 0
	t.startTime = time.Now()
}

// SlashCommandsComponent handles slash command processing
type SlashCommandsComponent struct {
	styles Styles
}

// NewSlashCommandsComponent creates a new slash commands component
func NewSlashCommandsComponent(styles Styles) *SlashCommandsComponent {
	return &SlashCommandsComponent{
		styles: styles,
	}
}

// GetHelpText returns the help text for slash commands
func (s *SlashCommandsComponent) GetHelpText() string {
	return `Available slash commands:
/help - Show this help message
/model [model_name] - Change the current model
/provider [provider_name] - Change the current provider  
/session new - Create a new session
/session list - List all sessions
/session load <id> - Load a specific session
/session delete <id> - Delete a session
/message-history - Show message history
/clear - Clear message history
/config - Show current configuration
/quit - Exit the application

Examples:
/model deepseek-chat
/provider ollama
/session new
/session load abc12345`
}

// ProcessCommand processes a slash command and returns the result
func (s *SlashCommandsComponent) ProcessCommand(command string, sess *session.Session) (string, error) {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return "", fmt.Errorf("empty command")
	}

	cmd := parts[0]
	args := parts[1:]

	switch cmd {
	case "/help":
		return s.GetHelpText(), nil

	case "/model":
		if len(args) == 0 {
			cfg := config.Get()
			return fmt.Sprintf("Current model: %s", cfg.Provider.Model), nil
		}
		// TODO: Implement model switching
		return fmt.Sprintf("Model switching to %s (not implemented yet)", args[0]), nil

	case "/provider":
		if len(args) == 0 {
			cfg := config.Get()
			return fmt.Sprintf("Current provider: %s", cfg.Provider.Name), nil
		}
		// TODO: Implement provider switching
		return fmt.Sprintf("Provider switching to %s (not implemented yet)", args[0]), nil

	case "/session":
		return s.handleSessionCommand(args, sess)

	case "/message-history":
		return s.formatMessageHistory(sess), nil

	case "/clear":
		sess.ClearMessages()
		return "Message history cleared", nil

	case "/config":
		return s.formatConfig(), nil

	case "/quit":
		return "Goodbye!", nil

	default:
		return "", fmt.Errorf("unknown command: %s. Type /help for available commands", cmd)
	}
}

// handleSessionCommand handles session-related commands
func (s *SlashCommandsComponent) handleSessionCommand(args []string, sess *session.Session) (string, error) {
	if len(args) == 0 {
		return fmt.Sprintf("Current session: %s (%s)", sess.ID, sess.Name), nil
	}

	switch args[0] {
	case "new":
		return "Creating new session (not implemented yet)", nil
	case "list":
		return "Listing sessions (not implemented yet)", nil
	case "load":
		if len(args) < 2 {
			return "", fmt.Errorf("session load requires a session ID")
		}
		return fmt.Sprintf("Loading session %s (not implemented yet)", args[1]), nil
	case "delete":
		if len(args) < 2 {
			return "", fmt.Errorf("session delete requires a session ID")
		}
		return fmt.Sprintf("Deleting session %s (not implemented yet)", args[1]), nil
	default:
		return "", fmt.Errorf("unknown session command: %s", args[0])
	}
}

// formatMessageHistory formats the message history for display
func (s *SlashCommandsComponent) formatMessageHistory(sess *session.Session) string {
	messages := sess.GetMessages()
	if len(messages) == 0 {
		return "No messages in current session"
	}

	var result strings.Builder
	result.WriteString(fmt.Sprintf("Message History (%d messages):\n", len(messages)))
	
	for i, msg := range messages {
		timestamp := msg.Timestamp.Format("15:04:05")
		result.WriteString(fmt.Sprintf("%d. [%s] %s: %s\n", 
			i+1, timestamp, strings.Title(msg.Role), 
			truncateString(msg.Content, 100)))
	}

	return result.String()
}

// formatConfig formats the current configuration for display
func (s *SlashCommandsComponent) formatConfig() string {
	cfg := config.Get()
	
	return fmt.Sprintf(`Current Configuration:
Provider: %s
Model: %s
Base URL: %s
Auto Approve: %t
Confirm Commands: %t
Show Timestamps: %t
Max History: %d
Session Timeout: %d seconds`,
		cfg.Provider.Name,
		cfg.Provider.Model,
		cfg.Provider.BaseURL,
		cfg.Terminal.AutoApprove,
		cfg.Terminal.ConfirmCommands,
		cfg.Terminal.ShowTimestamps,
		cfg.Session.MaxHistory,
		cfg.Session.SessionTimeout)
}

// truncateString truncates a string to the specified length
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}
